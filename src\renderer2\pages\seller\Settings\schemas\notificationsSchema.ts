import * as yup from 'yup';

// Define all possible notification types
export const NOTIFICATION_TYPES = {
  USER_ACCOUNT: {
    PASSWORD_UPDATES: 'passwordUpdates',
  },
  ORDER_UPDATES: {
    NEW_ORDERS_TO_PREVIEW: 'newOrdersToPreview',
    ORDERS_READY_TO_CLAIM: 'ordersReadyToClaim',
    ORDER_CONFIRMATIONS: 'orderConfirmations',
    ORDER_CHANGES: 'orderChanges',
    ORDER_CANCELATIONS: 'orderCancelations',
    PAYMENT_REMITTANCES: 'paymentRemittances',
    VENDOR_CHAT_MESSAGING: 'vendorChatMessaging',
  },
  APP_FEATURES: {
    APP_UPDATES: 'appUpdates',
    PRICING_UPDATES: 'pricingUpdates',
    NEW_VIDEO_CONTENT: 'newVideoContent',
    FEEDBACK_REQUESTS: 'feedbackRequests',
  }
};

// Notification categories and items for UI mapping
export const NOTIFICATION_CATEGORIES = [
  {
    id: 'userAccount',
    title: 'USER ACCOUNT',
    items: [
      { 
        id: NOTIFICATION_TYPES.USER_ACCOUNT.PASSWORD_UPDATES,
        title: 'PASSWORD UPDATES',
        tooltipText: 'Receive alerts for password changes or reset activity on your account.',
        defaultValues: { text: true, email: true, desktop: true }
      }
    ]
  },
  {
    id: 'orderUpdates',
    title: 'ORDER UPDATES',
    items: [
      { 
        id: NOTIFICATION_TYPES.ORDER_UPDATES.NEW_ORDERS_TO_PREVIEW,
        title: 'NEW ORDERS TO PREVIEW',
        tooltipText: 'Get notified when a new order is available for preview.',
        defaultValues: { text: true, email: true, desktop: true }
      },
      { 
        id: NOTIFICATION_TYPES.ORDER_UPDATES.ORDERS_READY_TO_CLAIM,
        title: 'ORDERS READY TO CLAIM',
        tooltipText: 'Get notified when an order is ready to be claimed.',
        defaultValues: { text: true, email: true, desktop: true }
      },
      { 
        id: NOTIFICATION_TYPES.ORDER_UPDATES.ORDER_CONFIRMATIONS,
        title: 'ORDER CONFIRMATIONS',
        tooltipText: 'Get notified when your order has been successfully placed.',
        defaultValues: { text: true, email: true, desktop: true }
      },
      { 
        id: NOTIFICATION_TYPES.ORDER_UPDATES.ORDER_CHANGES,
        title: 'ORDER CHANGES',
        tooltipText: 'Receive updates when there are modifications to your existing order.',
        defaultValues: { text: false, email: false, desktop: false }
      },
      { 
        id: NOTIFICATION_TYPES.ORDER_UPDATES.ORDER_CANCELATIONS,
        title: 'ORDER CANCELATIONS',
        tooltipText: 'Get alerts if an order you placed is canceled.',
        defaultValues: { text: false, email: false, desktop: false }
      },
      { 
        id: NOTIFICATION_TYPES.ORDER_UPDATES.PAYMENT_REMITTANCES,
        title: 'PAYMENT REMITTANCES',
        tooltipText: 'Get confirmation when a payment has been processed and recorded.',
        defaultValues: { text: false, email: false, desktop: false }
      },
      { 
        id: NOTIFICATION_TYPES.ORDER_UPDATES.VENDOR_CHAT_MESSAGING,
        title: 'VENDOR CHAT MESSAGING',
        tooltipText: 'Be alerted when you receive a new message from a vendor regarding an order.',
        defaultValues: { text: false, email: false, desktop: false }
      },
    ]
  },
  {
    id: 'appFeatures',
    title: 'APP FEATURES',
    items: [
      { 
        id: NOTIFICATION_TYPES.APP_FEATURES.APP_UPDATES,
        title: 'APP UPDATES',
        tooltipText: 'Stay informed about new features, improvements, or changes to the app.',
        defaultValues: { text: true, email: true, desktop: true }
      },
      { 
        id: NOTIFICATION_TYPES.APP_FEATURES.PRICING_UPDATES,
        title: 'PRICING UPDATES',
        tooltipText: 'Receive alerts when product pricing changes or is updated.',
        defaultValues: { text: true, email: true, desktop: true }
      },
      { 
        id: NOTIFICATION_TYPES.APP_FEATURES.NEW_VIDEO_CONTENT,
        title: 'NEW VIDEO CONTENT',
        tooltipText: 'Get notified when new video content is available.',
        defaultValues: { text: true, email: true, desktop: true }
      },
      { 
        id: NOTIFICATION_TYPES.APP_FEATURES.FEEDBACK_REQUESTS,
        title: 'FEEDBACK REQUESTS',
        tooltipText: 'Receive alerts when feedback is requested for a product or service.',
        defaultValues: { text: true, email: true, desktop: true }
      },
    ]
  }
];

// Type definitions for notification preferences
type NotificationPreferences = {
  [key: string]: boolean;
};

// Create schema for notification preferences
export const notificationsSchema = yup.object().shape({
  // Text notification preferences
  textNotifications: yup.object({
    [NOTIFICATION_TYPES.USER_ACCOUNT.PASSWORD_UPDATES]: yup.boolean().default(true),
    
    [NOTIFICATION_TYPES.ORDER_UPDATES.NEW_ORDERS_TO_PREVIEW]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.ORDER_UPDATES.ORDERS_READY_TO_CLAIM]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.ORDER_UPDATES.ORDER_CONFIRMATIONS]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.ORDER_UPDATES.ORDER_CHANGES]: yup.boolean().default(false),
    [NOTIFICATION_TYPES.ORDER_UPDATES.ORDER_CANCELATIONS]: yup.boolean().default(false),
    [NOTIFICATION_TYPES.ORDER_UPDATES.PAYMENT_REMITTANCES]: yup.boolean().default(false),
    [NOTIFICATION_TYPES.ORDER_UPDATES.VENDOR_CHAT_MESSAGING]: yup.boolean().default(false),
    
    [NOTIFICATION_TYPES.APP_FEATURES.APP_UPDATES]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.APP_FEATURES.PRICING_UPDATES]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.APP_FEATURES.NEW_VIDEO_CONTENT]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.APP_FEATURES.FEEDBACK_REQUESTS]: yup.boolean().default(true),
  }),
  
  // Email notification preferences
  emailNotifications: yup.object({
    [NOTIFICATION_TYPES.USER_ACCOUNT.PASSWORD_UPDATES]: yup.boolean().default(true),
    
    [NOTIFICATION_TYPES.ORDER_UPDATES.NEW_ORDERS_TO_PREVIEW]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.ORDER_UPDATES.ORDERS_READY_TO_CLAIM]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.ORDER_UPDATES.ORDER_CONFIRMATIONS]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.ORDER_UPDATES.ORDER_CHANGES]: yup.boolean().default(false),
    [NOTIFICATION_TYPES.ORDER_UPDATES.ORDER_CANCELATIONS]: yup.boolean().default(false),
    [NOTIFICATION_TYPES.ORDER_UPDATES.PAYMENT_REMITTANCES]: yup.boolean().default(false),
    [NOTIFICATION_TYPES.ORDER_UPDATES.VENDOR_CHAT_MESSAGING]: yup.boolean().default(false),
    
    [NOTIFICATION_TYPES.APP_FEATURES.APP_UPDATES]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.APP_FEATURES.PRICING_UPDATES]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.APP_FEATURES.NEW_VIDEO_CONTENT]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.APP_FEATURES.FEEDBACK_REQUESTS]: yup.boolean().default(true),
  }),
  
  // Desktop notification preferences
  desktopNotifications: yup.object({
    [NOTIFICATION_TYPES.USER_ACCOUNT.PASSWORD_UPDATES]: yup.boolean().default(true),
    
    [NOTIFICATION_TYPES.ORDER_UPDATES.NEW_ORDERS_TO_PREVIEW]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.ORDER_UPDATES.ORDERS_READY_TO_CLAIM]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.ORDER_UPDATES.ORDER_CONFIRMATIONS]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.ORDER_UPDATES.ORDER_CHANGES]: yup.boolean().default(false),
    [NOTIFICATION_TYPES.ORDER_UPDATES.ORDER_CANCELATIONS]: yup.boolean().default(false),
    [NOTIFICATION_TYPES.ORDER_UPDATES.PAYMENT_REMITTANCES]: yup.boolean().default(false),
    [NOTIFICATION_TYPES.ORDER_UPDATES.VENDOR_CHAT_MESSAGING]: yup.boolean().default(false),
    
    [NOTIFICATION_TYPES.APP_FEATURES.APP_UPDATES]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.APP_FEATURES.PRICING_UPDATES]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.APP_FEATURES.NEW_VIDEO_CONTENT]: yup.boolean().default(true),
    [NOTIFICATION_TYPES.APP_FEATURES.FEEDBACK_REQUESTS]: yup.boolean().default(true),
  }),
  
  // Phone number for text notifications
  phoneNumber: yup.string().when('textNotifications', {
    is: (textNotifications: NotificationPreferences) => Object.values(textNotifications).some(value => value === true),
    then: () => yup.string().required('Phone number is required for text notifications'),
    otherwise: () => yup.string()
  }),
  
  // Email address for email notifications
  email: yup.string().when('emailNotifications', {
    is: (emailNotifications: NotificationPreferences) => Object.values(emailNotifications).some(value => value === true),
    then: () => yup.string().email('Invalid email address').required('Email is required for email notifications'),
    otherwise: () => yup.string().email('Invalid email address')
  }),
});

export type NotificationsFormData = yup.InferType<typeof notificationsSchema>; 