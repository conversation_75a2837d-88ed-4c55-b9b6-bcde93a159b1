import React from 'react';
import styles from './NotificationsTab.module.scss';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { notificationsSchema, NOTIFICATION_CATEGORIES, NotificationsFormData } from '../schemas/notificationsSchema';
import clsx from 'clsx';
import { Tooltip, Fade } from '@mui/material';

// Custom notification checkbox component
const NotificationCheckbox: React.FC<{
  type: 'text' | 'email' | 'desktop';
  name: string;
  control: any;
  defaultChecked?: boolean;
}> = ({ type, name, control, defaultChecked = false }) => {
  const label = type === 'text' ? 'T' : type === 'email' ? 'E' : 'D';
  
  return (
    <Controller
      name={name}
      control={control}
      defaultValue={defaultChecked}
      render={({ field: { onChange, value } }) => (
        <div 
          className={clsx(
            styles.notificationCheckbox, 
            {
              [styles.textType]: type === 'text',
              [styles.emailType]: type === 'email',
              [styles.desktopType]: type === 'desktop',
              [styles.checked]: value
            }
          )}
          onClick={() => onChange(!value)}
        >
          {label}
        </div>
      )}
    />
  );
};

const NotificationsTab: React.FC = () => {
  const { control, watch } = useForm<NotificationsFormData>({
    resolver: yupResolver(notificationsSchema),
    mode: "onBlur"
  });

  console.log("watch", watch());

  return (
    <div className={styles.notificationMainContainer}>
      <div className={styles.notificationHeader}>
        Some notifications are required and unable to opt-out. Select the notifications you would
        like to receive and how you would like receive them: by
        <span className={styles.methodText}> Text <span className={clsx(styles.methodIcon, styles.textIcon)}>T</span>&nbsp;</span>,
        <span className={styles.methodText}> Email <span className={clsx(styles.methodIcon, styles.emailIcon)}>E</span>&nbsp;</span>, or
        <span className={styles.methodText}> Desktop <span className={clsx(styles.methodIcon, styles.desktopIcon)}>D</span>&nbsp;</span>.
      </div>
      <div className={styles.notificationContainer}>
        <div className={styles.notificationSection}>
          <div className={styles.notificationSectionTitle}>MOBILE NUMBER</div>
          <div className={styles.notificationSectionContent}>
            <div className={styles.notificationItem}>
              <span>(*************</span>
              <div className={styles.notificationToggle}>
                <button>CHANGE NUMBER</button>
              </div>
            </div>
          </div>
        </div>
        {NOTIFICATION_CATEGORIES.map(category => (
          <div  key={category.id} className={styles.notificationSection}>
            <div className={styles.notificationSectionTitle}>{category.title}</div>
            
              <div className={styles.notificationSectionContent}>
              {category.items.map(item => (
                <div className={styles.notificationItem}>
                  <span>{item.title}</span>
                  <div className={styles.notificationToggle}>             
                    <div  className={clsx(styles.notificationCheckbox, styles.textType)}>T</div>
                    <div  className={clsx(styles.notificationCheckbox, styles.textType)}>E</div>
                    <div  className={clsx(styles.notificationCheckbox, styles.textType)}>D</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default NotificationsTab; 