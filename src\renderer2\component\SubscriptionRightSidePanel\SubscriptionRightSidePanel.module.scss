.subscriptionRightSidePanel{
    height: 100vh;
    width: 500px;
    background-color: #1a1a1a;
    display: flex;
    flex-direction: column;
    border: 1px solid #0066cc;
    border-radius: 0 0 8px 8px;
    overflow: hidden;
}

.header {
    background-color: #0066cc;
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60px;
}

.headerText {
    color: white !important;
    font-weight: bold !important;
    font-size: 18px !important;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.content {
    flex: 1;
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.textContent {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.paragraph {
    color: white !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    margin: 0 !important;
}

.dropdownSection {
    margin-top: 16px;
}

.formControl {
    width: 100%;
}

.select {
    background-color: #333333 !important;
    border-radius: 8px !important;
    color: white !important;
    
    & .MuiSelect-select {
        color: white !important;
        padding: 12px 16px !important;
        font-weight: bold !important;
    }
    
    & .MuiOutlinedInput-notchedOutline {
        border: none !important;
    }
    
    & .MuiSelect-icon {
        color: white !important;
    }
}

.menuPaper {
    background-color: #333333 !important;
    border: 1px solid #555555 !important;
    
    & .MuiMenuItem-root {
        color: white !important;
        padding: 12px 16px !important;
        
        &:hover {
            background-color: #444444 !important;
        }
        
        &.Mui-selected {
            background-color: #0066cc !important;
        }
    }
}

.placeholderText {
    color: white !important;
    font-weight: bold !important;
    font-size: 14px !important;
}

.menuItemText {
    color: white !important;
    font-size: 14px !important;
}

.actionSection {
    margin-top: auto;
    padding-top: 16px;
}

.doLaterButton {
    background: none !important;
    border: none !important;
    padding: 0 !important;
    min-width: auto !important;
    text-transform: none !important;
    box-shadow: none !important;
    
    &:hover {
        background: none !important;
        box-shadow: none !important;
    }
}

.doLaterText {
    color: #cccccc !important;
    font-size: 14px !important;
    text-decoration: underline;
    cursor: pointer;
    
    &:hover {
        color: #ffffff !important;
    }
}