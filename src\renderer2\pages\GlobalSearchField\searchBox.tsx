import React, { useState, useRef, useEffect } from "react";
import { ClickAwayListener } from "@mui/material";
import Button from "@mui/material/Button";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import "./SearchBox.css"; // for styling
import { options, purchaseOrder, routes } from "src/renderer2/common";
import { useNavigate,useLocation  } from "react-router-dom";
import { Widgets } from "@mui/icons-material";
import { searchProducts, useCreatePoStore, useGlobalClockStore, useGlobalStore, useSearchStore, useSellerOrderStore, useSubscriptionStore } from "@bryzos/giss-ui-library";
import { filterObjectsByString, flattenObject, formatToTwoDecimalPlaces } from "src/renderer2/helper";
import { useGlobalSearchStore } from "./globalSearchStore";
import { CustomMenu } from "../buyer/CustomMenu";
import SearchResultMenu from "./searchResultMenu";
import styles from "./searchBox.module.scss";
import { ReactComponent as SearchIcon } from "../../assets/New-images/Search.svg";
import { ReactComponent as DropdownIcon } from "../../assets/New-images/New-Image-latest/Polygon.svg";
import { ReactComponent as CheckIcon } from "../../assets/New-images/New-Image-latest/icon-check-dropdown.svg";
import useGetDraftLines from "src/renderer2/hooks/useGetDraftLines";
import { useOrderManagementStore } from "src/renderer2/store/OrderManagementStore";
import moment from "moment";

const SearchBox: React.FC<{ onSubscribeClick: () => void }> = ({ onSubscribeClick }) => {
  const [isFocused, setIsFocused] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [searchAnchorEl, setSearchAnchorEl] = useState<null | HTMLElement>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const open = Boolean(anchorEl);
  const {savedSearchProducts } = useSearchStore();
  const {quoteList, purchasingList}  = useCreatePoStore();
  const [flattenedPreviewOrderList, setFlattenedPreviewOrderList] = useState([]);
  const [flattenedClaimOrderList, setFlattenedClaimOrderList] = useState([]);
  const [flattenedDeleteOrderList, setFlattenedDeleteOrderList] = useState([]);
  const purchaseOrdersList = useSellerOrderStore((state: any) => state.ordersCart);
  const [flattenedSavedSearchProducts, setFlattenedSavedSearchProducts] = useState([]);
  const [flattenedOrderManagementData, setFlattenedOrderrManagementData] = useState([]);
  const [flattenedQuoteList, setFlattenedQuoteList] = useState([]);
  const [flattenedPurchasingList, setFlattenedPurchasingList] = useState([]);
  const [filteredObjects, setFilteredObjects] = useState([]);
  const { setSelectedIndex, setSelectedObject, createPoUpdatedData, setKeepFocus, keepFocus } = useGlobalSearchStore();
  const {userData} = useGlobalStore();
  const [searchText, setSearchText] = useState('');
  const {mutateAsync: getDraftLines} = useGetDraftLines();
  const {orderManagementData} = useOrderManagementStore();
  const {userSubscription} = useSubscriptionStore();
  const searchInputRef = useRef<HTMLInputElement>(null);
  const {referenceData } = useGlobalStore();




  const ignoreFields = [
    /^payment_method$/,
    /^order_type$/,
    /^shipping_details\.state_id$/,
    /^freight_term$/,
    /^source$/,
    /^bom_id$/,
    /^pricing_expired$/,
  
    // cart_items with dynamic index
    /^cart_items#\d+\.id$/,
    /^cart_items#\d+\.line_id$/,
    /^cart_items#\d+\.product_id$/,
    /^cart_items#\d+\.domestic_material_only$/,
    /^cart_items#\d+\.line_history$/
  ];

  const [options, setOptions] = useState([]);

  const buyerOptions = [
    {label:'Instant Price Search', route:routes.homePage},
    {label:'Quoting', route:routes.quotePage},
    {label:'Purchasing', route:routes.createPoPage},
    {label:'Order Management', route:routes.orderManagementPage}
    ];

  const sellerOptions = [
    {label:'Prview Orders', route:routes.previewOrderPage},
    {label:'Claim Orders', route:routes.orderPage},
    {label:'Order Management', route:routes.orderManagementPage},
    {label:'Deleted Items', route:routes.deleteOrderPage}
    ];

  const currentPath = location.pathname; // e.g., "/pricing"
  const [selectedOption, setSelectedOption] = useState(null);

useEffect(()=>{
  if(!userData) return;
  if(userData?.data?.type === 'SELLER'){
    setOptions(sellerOptions);
    setSelectedOption(sellerOptions[0]);
  }else{
    setOptions(buyerOptions);
    setSelectedOption(buyerOptions[0]);
  }
},[userData]);

useEffect(() => {
  if(!selectedOption) return;
  const newSelectedOption = options.find(option => currentPath.includes(option.route));
  setSelectedOption(newSelectedOption || options[0]);
}, [currentPath]);

useEffect(()=>{
  if(keepFocus){
    searchInputRef.current?.focus();
    setKeepFocus(false);
  }
},[location.pathname]);

useEffect(() => {
    if(createPoUpdatedData){
      //filter quoteList and purchasingList and remove if we get a match replace it
      if(createPoUpdatedData.order_type === 'QUOTE'){
        setFlattenedQuoteList(prev =>
          prev.map(item =>
            item.id === createPoUpdatedData.id ? { ...item, ...flattenObject(createPoUpdatedData,'',ignoreFields) } : item
          )
        );
      }else if(createPoUpdatedData.order_type === 'PO'){
        setFlattenedPurchasingList(prev =>
          prev.map(item =>
            item.id === createPoUpdatedData.id ? { ...item, ...flattenObject(createPoUpdatedData,'',ignoreFields) } : item
          )
        );
      }
    }
},[createPoUpdatedData]);

useEffect(() => {
  if (!purchaseOrdersList?.length) {
    setFlattenedClaimOrderList([]);
    setFlattenedPreviewOrderList([]);
    setFlattenedDeleteOrderList([]);
    return;
  }

  const claim: any[] = [];
  const preview: any[] = [];
  const removed: any[] = [];

  for (const order of purchaseOrdersList) {
    let state = referenceData?.ref_states?.find(stateDetail => stateDetail.id == order.state_id)?.code;
    let shapes = new Set(order.items.map(item => item.shape));
    let title = [...shapes].join(', ');
    const flat = flattenObject({ 
      created_date: order.created_date,
      id:order.id,
      items:order.items,
      delivery_date_short:moment.utc(order.delivery_date).tz('America/Chicago').format('M/DD/YY'), 
      delivery_date_normal:moment.utc(order.created_date).tz('America/Chicago').format('MMM DD, YYYY'),
      total_wieght_formatted:formatToTwoDecimalPlaces(order.total_weight) + 'LBS',
      total_order_value_formatted:'$' + formatToTwoDecimalPlaces((Number(order.seller_po_price) + Number(order.seller_sales_tax)).toFixed(2)),
      total_order_value_formatted2:'$ ' + formatToTwoDecimalPlaces((Number(order.seller_po_price) + Number(order.seller_sales_tax)).toFixed(2)),
      frieght_term_formatted:order.freight_term + (order.frieght_term === 'Delivered' ? 'FOB Destination (Delivered)' : 'FOB Origin (Prepaid)'),
      delivery_destination_long: order.city+  ", " + state + " " + order.zip,
      delivery_destination_short: order.city+  ", " + state,
      title: (title.length > 20)? title.substring(0, 20) + '...' : title
    }, '', ignoreFields); // flatten once, reuse

    if (order.claimed_by === purchaseOrder.readyToClaim) claim.push(flat);
    if (order.claimed_by === purchaseOrder.pending)      preview.push(flat);
    if (order.is_order_hidden === true)                  removed.push(flat);
  }

  setFlattenedClaimOrderList(claim);
  setFlattenedPreviewOrderList(preview);
  setFlattenedDeleteOrderList(removed);
}, [purchaseOrdersList]);

useEffect(() => {
    if(savedSearchProducts){
        const flattenedData = savedSearchProducts.map((product: any) => flattenObject(product,'',ignoreFields));
        
        setFlattenedSavedSearchProducts(flattenedData);
    }
},[savedSearchProducts]);

useEffect(() => {
  if (quoteList) {
    const loadData = async () => {
      const flattenedData = await Promise.all(
        quoteList.map(async (product: any) => {
          const tempFlattenedObj = flattenedQuoteList.find((item: any) => item.id === product.id);
          if (tempFlattenedObj ) {
            return tempFlattenedObj;
          }

          let cart_items = [];
          try {
            const res = await getDraftLines(product.id);
            cart_items = res.data;
          } catch (err) {
            console.error(`Failed to get draft lines for product ${product.id}`, err);
            cart_items = [];
          }
          const retObj = flattenObject({
            ...product,
            cart_items
          },'',ignoreFields);

          return retObj;
        })
      );

      setFlattenedQuoteList(flattenedData);
    };

    loadData();
  }
}, [quoteList]);


useEffect(() => {
  if (purchasingList) {
    const loadData = async () => {
      const flattenedData = await Promise.all(
        purchasingList.map(async (product: any) => {
          const tempFlattenedObj = flattenedPurchasingList.find((item: any) => item.id === product.id);
          
          if (tempFlattenedObj) {
            return tempFlattenedObj;
          }

          let cart_items = [];
          try {
            const res = await getDraftLines(product.id);
            cart_items = res.data;
          } catch (err) {
            console.error(`Failed to get draft lines for product ${product.id}`, err);
            cart_items = [];
          }
          const retObj = flattenObject({
            ...product,
            cart_items
          },'',ignoreFields);

          return retObj;
        })
      );

      setFlattenedPurchasingList(flattenedData);
    };

    loadData();
  }
}, [purchasingList]);

useEffect(()=>{
  if(orderManagementData){
    const flattenedData = orderManagementData.map((product: any) => flattenObject(product,'',ignoreFields));
    
    setFlattenedOrderrManagementData(flattenedData);
  }
},[orderManagementData]);

  const handleDropdownButtonClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
    setIsFocused(true);
  };

  const handleMenuItemClick = (option) => {
    if(option.route){
      navigate(option.route);
      //setIsFocused(false);
    }
    setKeepFocus(true);
    searchInputRef.current?.focus();
    setAnchorEl(null);
  };

  const handleClickAway = () => {
    setIsFocused(false);
    setAnchorEl(null); 
    setFilteredObjects([]);
    setSearchText('');
  };

  const handleSearchResultMenuClose = () => {
    setFilteredObjects([]);
    setSearchText('');
  };

  const searchChangeHandler = (event) => {
    const searchString = event.target.value;
    setSearchText(searchString);
    if(searchString.length < 3){
      setFilteredObjects([]);
      return;
    }
    let filteredData = [];
    if(userData.data.type === 'SELLER'){
      switch(selectedOption.route){
        case routes.previewOrderPage:
          filteredData = filterObjectsByString(flattenedPreviewOrderList, searchString);
          break;
        case routes.orderPage:
          filteredData = filterObjectsByString(flattenedClaimOrderList, searchString);
          break;
        case routes.deleteOrderPage:
          filteredData = filterObjectsByString(flattenedDeleteOrderList, searchString);
          break;
      }
    }else{
      switch(selectedOption.route){
        case routes.homePage:
          filteredData = filterObjectsByString(flattenedSavedSearchProducts, searchString);
          break;
        case routes.quotePage:
          filteredData = filterObjectsByString(flattenedQuoteList, searchString);
          break;
        case routes.createPoPage:
          filteredData = filterObjectsByString(flattenedPurchasingList, searchString);
          break;
        case routes.orderManagementPage:
          filteredData = filterObjectsByString(flattenedOrderManagementData, searchString);
          break;
      }
    }
    
    setFilteredObjects(filteredData);
  };

  const handleSearchFocus = (event: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true);
    setSearchAnchorEl(event.currentTarget);
  };

  const handleSearchSelection = (obj: any) => {
      setFilteredObjects([]);
      setSearchText('');
      setIsFocused(false);
      if(userData.data.type === 'SELLER'){
        let id;
        switch(selectedOption.route){
          case routes.previewOrderPage:
            id = flattenedPreviewOrderList[obj.index].id;
            const previewItem = purchaseOrdersList.find((item: any) => item.id === id);
            setSelectedIndex(obj.index);
            setSelectedObject({...previewItem});
          break;
          case routes.orderPage:
          id = flattenedClaimOrderList[obj.index].id;
          const claimItem = purchaseOrdersList.find((item: any) => item.id === id);
            setSelectedIndex(obj.index);
            setSelectedObject({...claimItem});
            break;
          case routes.orderManagementPage:
            id = flattenedOrderrManagementData[obj.index].id;
            const orderItem = orderManagementData.find((item: any) => item.id === id);
            setSelectedIndex(obj.index);
            setSelectedObject({...orderItem});
            break;
          case routes.deleteOrderPage:
            id = flattenedDeleteOrderList[obj.index].id;
            const deletedItem = purchaseOrdersList.find((item: any) => item.id === id);
            setSelectedIndex(obj.index);
            setSelectedObject({...deletedItem});
            break;
        }
      }else{
        switch(selectedOption.route){
          case routes.homePage:
            const item = savedSearchProducts[ obj.index];
            setSelectedIndex(obj.index);
            setSelectedObject({...item});
            break;
          case routes.quotePage:
            const quoteItem = quoteList[ obj.index];
            setSelectedIndex(obj.index);
            setSelectedObject({...quoteItem});
            break;
          case routes.createPoPage:
            const poItem = purchasingList[ obj.index];
            setSelectedIndex(obj.index);
            setSelectedObject({...poItem});
            break;
          case routes.orderManagementPage:
            const orderItem = orderManagementData[ obj.index];
            setSelectedIndex(obj.index);
            setSelectedObject({...orderItem});
            break;
        }
      }
    }
  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <div className="search-container" >
        <div className="search-button-and-field">
          {isFocused ? (
            <>
              <Button
                className={styles.searchTypeBtn}
                onClick={handleDropdownButtonClick}
              >
                {selectedOption.label} <DropdownIcon className={styles.dropdownIcon} />
              </Button>
              <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={() => setAnchorEl(null)}
                PaperProps={{
                  className: styles.dropdownMenu,
                  style: {
                    width: anchorEl?.offsetWidth || 300,
                    backgroundColor: 'transparent',
                  },
                }}
              >
                {options.map((option, index) => (
                  <MenuItem key={index} onClick={() => handleMenuItemClick(option)} disabled={!option.route}>
                    {selectedOption.label === option.label && <CheckIcon />}{option.label}
                  </MenuItem>
                ))}
              </Menu>
            </>
          ) : (<>
          {
            ((!userSubscription?.subscription_id || Object.keys(userSubscription).length === 0) && userData?.data?.type !== 'SELLER') && (
              <Button className={styles.subscribeBtn} onClick={onSubscribeClick} variant="contained">
                Subscribe
              </Button>
            )
          }
          </>
          )}
          <div className={styles.globalSearchField}>
            <SearchIcon className={styles.searchIcon} />
            <input
              ref={searchInputRef}
              className={styles.searchInput}
              placeholder="Search Your Account"
              value={searchText}
              onFocus={handleSearchFocus}
              onChange={searchChangeHandler}
              style={isFocused ? {borderRadius: '0px 30px 30px 0px'} : {borderRadius: '30px'}}
            />
            <SearchResultMenu
              anchorEl={searchAnchorEl}
              open={filteredObjects.length > 0}
              onClose={handleSearchResultMenuClose}
              items={filteredObjects}
              onItemClick={handleSearchSelection}
              PaperProps={{
                className: styles.dropdownMenu,
                style: {
                  minWidth: searchAnchorEl?.offsetWidth || 300,
                  backgroundColor: 'transparent',
                },
              }}
            />
            {/* <Menu
              anchorEl={searchAnchorEl}
              open={filteredObjects.length > 0}
              onClose={() => setFilteredObjects([])}
              PaperProps={{
                style: {
                  maxHeight: 300,
                  width: searchAnchorEl?.offsetWidth || 300,
                },
              }}
            >
              {filteredObjects.map((obj, idx) => (
                <MenuItem
                  key={idx}
                  onClick={() => handleSearchSelection(obj)}
                >
                <div>
                  <div style={{ fontWeight: 500 }}>{obj.filteredObj[obj.field].length > 15 ? obj.filteredObj[obj.field].slice(0, 15) + "..." : obj.filteredObj[obj.field]}</div>
                </div>
                </MenuItem>
              ))}
            </Menu> */}
          </div>
        </div>
      </div>
    </ClickAwayListener>
  );
};

export default SearchBox;
