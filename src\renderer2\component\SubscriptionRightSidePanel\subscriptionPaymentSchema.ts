import * as yup from 'yup';

export const subscriptionPaymentSchema = yup.object().shape({
  // BNPL
  bnplAvailable: yup.boolean(),
  net30CheckBox: yup.string(),
  einNumber: yup.string().trim().test("isRequired", "Ein Number is not valid", function (value) {
    if (!/^x{5}\d{4}$|^\d{2}-\d{7}$/.test(value)) {
      return false
    }
    return !!value;
  }),
  dnBNumber: yup.string().test("isRequired", "D&B Number is not valid", function (value) {
    if (!/^x{5}\d{4}$|^\d{9}$/.test(value)) {
      return false
    }
    return !!value;
  }),
  creditLine: yup.string().test("isRequired", "Credit Line is not valid", function (value) {
    if (value) {
      return +value > 0;
    } else {
      return false;
    }
  }),
  requestedCreditLimit: yup.string(),
  balanceCreditLimit: yup.string(),



  // Credit Card
  cardType: yup.string(),
  cardNumberLast4Digits: yup.string(),
  cardExpiry: yup.string(),
  cardEmailId: yup.string(),
  cardFirstName: yup.string(),
  cardLastName: yup.string(),
  billingZipCode: yup.string().min(5, 'Zip is not valid'),

});

export type SubscriptionPaymentFormData = yup.InferType<typeof subscriptionPaymentSchema>; 