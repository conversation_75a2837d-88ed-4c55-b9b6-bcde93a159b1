import { create } from "zustand";

type OrderManagementStoreType = {
    orderManagementData: any[];
    setOrderManagementData: (orderManagementData: any) => void;
    resetOrderManagementStore: () => void;
    isEditingPo: boolean;
    setIsEditingPo: (isEditingPo: boolean) => void;
}

const commonOrderManagementStore = {
    orderManagementData: [],
    isEditingPo: false,
}

export const useOrderManagementStore = create<OrderManagementStoreType>((set) => ({
    ...commonOrderManagementStore,
    setOrderManagementData: (orderManagementData: any) => set({orderManagementData}),
    resetOrderManagementStore: () => set({...commonOrderManagementStore}),
    setIsEditingPo: (isEditingPo: boolean) => set({isEditingPo}),
}))