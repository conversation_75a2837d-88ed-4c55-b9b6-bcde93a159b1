import React, { useState, useEffect } from 'react'
import styles from './NextPaymentDetail.module.scss'
import { calculateSubscriptionAmount, formatDateForDisplay } from 'src/renderer2/helper';
import { useBuyerSettingStore, useSubscriptionStore } from '@bryzos/giss-ui-library';
import { PAYMENT_METHODS } from 'src/renderer2/common';

interface PaymentDetails {
  amount: number;
  currency: string;
  paymentDate: string;
  cardType: string;
  lastFourDigits: string;
}

interface NextPaymentDetailProps {
  initialPaymentDetails?: Partial<PaymentDetails>;
  onPaymentDetailsChange?: (details: PaymentDetails) => void;
}

const NextPaymentDetail: React.FC<NextPaymentDetailProps> = ({ 
  initialPaymentDetails,
  onPaymentDetailsChange
}) => {
  const { userSubscription , subscriptionsPricing } = useSubscriptionStore();
  const {buyerSetting} = useBuyerSettingStore();
  const [paymentDetails, setPaymentDetails] = useState<any>({
    nextPaymentCyclePrice: 0,
    nextPaymentCycleDay: 0,
  });
  useEffect(() => {
    if(userSubscription?.subscription_id && subscriptionsPricing){
      console.log("userSubscription", userSubscription)
      const pricingData = subscriptionsPricing;
      const nextPaymentCyclePrice = Number(pricingData?.price_amount)* Number(userSubscription?.licenses?.current_total);
      
      setPaymentDetails({
        nextPaymentCyclePrice: nextPaymentCyclePrice,
        nextPaymentCycleDay: userSubscription?.billing?.next_billing_date,
      })
    }else{
      console.log("userSubscription", userSubscription)
    }
  }, [userSubscription])

  console.log("paymentDetails", paymentDetails)
  return (
    <div className={styles.nextPaymentContainer}>
      <div className={styles.paymentInfo}>
        <p className={styles.paymentText}>
          Your next automatic payment is for{' '}
          <span className={styles.amount}>
            {paymentDetails.nextPaymentCyclePrice}
          </span>{' '}
          on {formatDateForDisplay(paymentDetails.nextPaymentCycleDay)}.
        </p>
        <p className={styles.cardInfo}>
          {
            userSubscription?.billing?.payment_method === PAYMENT_METHODS.CARD ? (
              <p>
                Auto-Debit from {buyerSetting?.card?.card_display_brand} ending in {buyerSetting?.card?.card_number_last_four_digits}
              </p>
            ) : (
              <p>
                ACH Debit from {buyerSetting?.ach_debit?.bank_name} account ending in {buyerSetting?.ach_debit?.account_number?.slice(-4)}
              </p>
            )
          }
        </p>
      </div>
    </div>
  )
}

export default NextPaymentDetail